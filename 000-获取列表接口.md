
000-获取列表接口


```python
import http.client

conn = http.client.HTTPSConnection("meeting.tencent.com")

payload = "{\"begin_time\":\"0\",\"end_time\":\"0\",\"meeting_code\":\"\",\"page_index\":1,\"page_size\":10,\"aggregationFastRecording\":0,\"cover_image_type\":\"meetlog_list_webp\",\"record_type_v4\":\"fast_record|cloud_record|user_upload|realtime_transcription|voice_record\",\"sort_by\":\"uni_record_id\",\"record_scene\":1}"

headers = {
  'User-Agent': "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36",
  'Accept': "application/json, text/plain, */*",
  'Accept-Encoding': "gzip, deflate, br, zstd",
  'Content-Type': "application/json",
  'web-caller': "my_meetings",
  'sec-ch-ua-platform': "\"Windows\"",
  'sec-ch-ua': "\"Not)A;Brand\";v=\"8\", \"Chromium\";v=\"138\", \"Google Chrome\";v=\"138\"",
  'sec-ch-ua-mobile': "?0",
  'origin': "https://meeting.tencent.com",
  'sec-fetch-site': "same-origin",
  'sec-fetch-mode': "cors",
  'sec-fetch-dest': "empty",
  'referer': "https://meeting.tencent.com/user-center/meeting-record",
  'accept-language': "zh-CN,zh;q=0.9",
  'priority': "u=1, i",
  'Cookie': "_qimei_uuid42=18c100b2d08100a8a8fbf14c894cfed7d943272246; _qimei_fingerprint=9a1333709ea85a9b315d1eb15950c460; _qimei_q36=; _ga_RPMZTEBERQ=GS1.1.1735105631.2.1.1735105719.0.0.0; _qimei_i_3=60c15380920c53dcc593ad665a8425e6f1e9a7f5100d0b87b58a7e582490756564363e943989e2a0b38e; hy_source=web; _gcl_au=1.1.1022510828.1748236605; hy_user=6a66ee4e7c9f42c7bc51292a37abe266; hy_token=vpwRvqa5aJvKcNXYU2K0ThrCqgjRjqFhpgy1aUc+Yv0s9h/sR38bKUm8RXzCMqCueFj/CEC+qCHMEC/JSJFAJA==; _qimei_h38=7de5d7f5a8fbf14c894cfed70200000f31930a; _qimei_i_1=2de24d85c10f558fc6c1fb6153d221b6f6eaf7f91b5f50d4e1db2f582493206c616361963980e1dc87f1e4c0; qcloud_from=qcloud.baidu.seo-1752558156326; qcstats_seo_keywords=%E9%80%9A%E7%94%A8%E6%8A%80%E6%9C%AF-%E7%BC%96%E7%A8%8B%E8%AF%AD%E8%A8%80-python%2C%E4%BA%91%E4%BA%A7%E5%93%81-%E5%AE%B9%E5%99%A8%E4%B8%8E%E4%B8%AD%E9%97%B4%E4%BB%B6-%E6%97%A5%E5%BF%97; sensorsdata2015jssdkcross=%7B%22distinct_id%22%3A%22100009325222%22%2C%22first_id%22%3A%22194f4284b9afe2-05e3105d450a0b-26011951-4953600-194f4284b9b2700%22%2C%22props%22%3A%7B%22%24latest_traffic_source_type%22%3A%22%E8%87%AA%E7%84%B6%E6%90%9C%E7%B4%A2%E6%B5%81%E9%87%8F%22%2C%22%24latest_utm_medium%22%3A%22cpc%22%2C%22%24search_keyword_id%22%3A%22f12eedaf0177fc23000000066881d78f%22%2C%22%24search_keyword_id_type%22%3A%22baidu_seo_keyword_id%22%2C%22%24search_keyword_id_hash%22%3A6719589322102133%7D%2C%22identities%22%3A%22eyIkaWRlbnRpdHlfY29va2llX2lkIjoiMTk0ZjQyODRiOWFmZTItMDVlMzEwNWQ0NTBhMGItMjYwMTE5NTEtNDk1MzYwMC0xOTRmNDI4NGI5YjI3MDAiLCIkaWRlbnRpdHlfbG9naW5faWQiOiIxMDAwMDkzMjUyMjIifQ%3D%3D%22%2C%22history_login_id%22%3A%7B%22name%22%3A%22%24identity_login_id%22%2C%22value%22%3A%22100009325222%22%7D%2C%22%24device_id%22%3A%22194f4284b9afe2-05e3105d450a0b-26011951-4953600-194f4284b9b2700%22%7D; we_meet_token=eJxUkV9vmzAUxb*LX7tMBtv8ibSHtWoyNRlpm5DSJ2TsC3ghQGxD0k777lMoUVPe*J1zbZ97-qLNcv1d1cbyWkCqJJoihr4NlLdt2g3EodRxmEuYg30ceoSQ0WLkLj3bBpeLPz5nFOHUKg0pzy1oNEUepgHGo9aDNqqphymX4sCl58lRzDqjajAGTdERshFatYfzU3xGfB-jIBx51RSqTu1bC4NdlNyOioReXTKNSEPxcWvgXRKoAk3R0w33CY7L4HVv2IKefonTjJyCZnXzclw1L63qG-JUwUPExbyNN9s5XxzrO-4uoHz0wnIWkdf1c-W*qkm0s-ywvd*6*8rwRZu85QUnh75hsWSwXJf8zwISmsT1LNIPXVLeHfvdRs2y6Pk2SnyNs8SqW7YMq9Ar-MfwEN93y7z-PZe9dW2e-Nz9uFr9dbSD2oMi3idodSM7Yb*YzPBDHSKoCPGE5iSf0ACTCQcXJhlImbsez5j8XI8k*uoALkTT1TYVjR5b9yjzmOPTSx*dAX2pw0H--gcAAP--PTK3Dw__; account_corp_id=*********; corp_id=*********; app_uid=144115235107096333; token_expire_time=**********; user_type=1; ACTIVITY_TICKET=eJxMkl1zojwcxb9Lbn3mMSEvgDN7sVZ3XaxF61axN0wgESPy0hCwdGe-%2A04ROubuf-45c05%2Akz-g9%2AP2fx7HRZ2b0LSlBBMA-utUJWRu1FFJDSbAgreDQkQIQtTCFEEbugxj3N-nZalEyE2ItQATAHu5EmnYrcAEIIhs23W-LPK9VFqG-Gi6EOTAwdVIXaki75ItAh2LfKb3S6Oyz57Ipti2IXTcXr8UicqHV1xlfOJmKKESMAFp8xL8XMzWeqcsX8Sb18P0PWuWlzjw9stR6nuL-dQr6m2iL3wusrympTT6IXBKfxNtfvl4ObtQz3uy2khoOD17LVnt34qqDvIzfzDZEz9F19ePlxVtZ-tD5o2r0TPbW%2AlptV40y8MuiPicUJaMk-FouYsif75dXdfPfrt1S8f7vuDpY7ypfpzPcvWtL65lcsPQz0I2KpZhR-MO8P1silTm4Zcx%2Aej1N5VJhdmdsXNZjB2xIzkTWBJGkYOYdI9YMiEoiejAfPgjcaHLWxwjlFFkk4F%2AXUk9wEfg778AAAD--w%2AAtUg_; lz_sign=KPcEAexvXPDfrJdvCsbqrj9rNhkVdwsj5lLzFVuyPzHm585djl2z7EF7FVkZIVUPT_gnmwuq5SInu8GvrMW3kWdBvPVaa17VxiYYAJ4-Yf0; lz_appid=*********; lz_uid=144115235107096333; lz_time=*************; landing_url=https://meeting.tencent.com/ct/24g6w13866; landing_path=https://meeting.tencent.com/ct/24g6w13866; landing_referralurl=; landing_referraldomain=; web_uid=97ce1f84-3627-47a8-b446-cc451a773ba2; _gid=GA1.2.*********.**********; _gat_UA-*********-1=1; lz_expire=*************; _ga=GA1.2.**********.**********; _ga_6WSZ0YS5ZQ=GS2.1.s1753770095$o1$g1$t1753770139$j16$l0$h0"
}

conn.request("POST", "/wemeet-tapi/v2/meetlog/dashboard/my-record-list?c_app_id=&c_os_model=web&c_os=web&c_os_version=Mozilla%2F5.0+%28Windows+NT+10.0%3B+Win64%3B+x64%29+AppleWebKit%2F537.36+%28KHTML%2C+like+Gecko%29+Chrome%2F*********+Safari%2F537.36&c_timestamp=*************&c_nonce=zwPrMxGEj&c_app_version=&c_instance_id=5&c_account_corp_id=*********&rnds=zwPrMxGEj&c_app_uid=&c_district=0&trace-id=d0ce92aa72c87cb50c49751531e63461&c_lang=zh-CN", payload, headers)

res = conn.getresponse()
data = res.read()

print(data.decode("utf-8"))
```

返回数据：

```json
{
  "code": 0,
  "err_detail": "",
  "msg": "",
  "data": {
    "last_index": "*************",
    "records": [
      {
        "record_id": "a697cb68-7196-42e8-b7c2-90b79652c975",
        "invalid_reason": "",
        "recorder_uid": "144115235107096333",
        "status": 2,
        "view_count": 0,
        "cover_url": "https://wemeet-record-**********.file.myqcloud.com/resource/pic/rt_default_background.png/mid?sign=b43bfba992613d1f2f93b07243a7e7fd&t=**********",
        "video_url": "",
        "start_time": "*************",
        "end_time": "*************",
        "duration": "2871132",
        "size": "0",
        "title": "转写_AI-诺岚的快速会议",
        "description": "",
        "permission_type": 32,
        "password_switch": 0,
        "is_del": 0,
        "last_view_time": "0",
        "recorder_avatar": "https://meeting-75420.picgzc.qpic.cn/f074f9dc99c85e08120766ebe1ba5215c3e6e8344ccbabd4c68b7488b31b4f9b",
        "recorder_username": "AI-诺岚",
        "encode_record_id": "a697cb68-7196-42e8-b7c2-90b79652c975",
        "meeting_info": {
          "subject": "转写_AI-诺岚的快速会议",
          "meeting_code": "546898734",
          "meeting_id": "13064285038564104908",
          "meeting_code_mask": "40",
          "sub_meeting_id": "0"
        },
        "record_type": "realtime_transcription",
        "video_resolution_tag": "",
        "uni_record_id": "1928270469206065152",
        "record_state": 3,
        "video_width": 0,
        "video_height": 0,
        "jump_path": "/user-center/shared-record-info?id=a697cb68-7196-42e8-b7c2-90b79652c975&from=0&record_type=4",
        "record_count": 1,
        "privilege_tips": "所有人可查看",
        "password": "",
        "share_url": "v2/cloud-record/share?id=a697cb68-7196-42e8-b7c2-90b79652c975&from=3&record_type=4",
        "share_url_expired": "0",
        "meeting_type": 1,
        "file_state": 99,
        "is_rooms": 0,
        "viewer_status": 0,
        "file_id": "1928270469206065152",
        "hover_msg": {
          "msg_type": 1,
          "time_at": "*************"
        },
        "access_type": 0,
        "access_msg": "",
        "is_show_smart_tag": true,
        "role_type": 0,
        "activity_id": "",
        "activity_uid": "",
        "sharing_state": 0,
        "cover_cos_key": "",
        "jump_path_short": "ctw/2jj3Enw8be",
        "share_url_short": "ctm/2VRDbeXQ77",
        "jump_path_v2": "user-center/shared-record-info?id=a697cb68-7196-42e8-b7c2-90b79652c975&from=0&record_type=4",
        "share_url_v2": "v2/cloud-record/share?id=a697cb68-7196-42e8-b7c2-90b79652c975&from=3&record_type=4",
        "recorder_appid": "*********",
        "share_cover_url": "",
        "is_admin": false,
        "is_oversea": false,
        "preload_page": "detail-page",
        "share_scope": 1,
        "allow_delete": true,
        "allow_download": true
      },
      {
        "record_id": "7ea63ee4-31b6-4316-898a-5a72767028a5",
        "invalid_reason": "",
        "recorder_uid": "144115235107096333",
        "status": 2,
        "view_count": 0,
        "cover_url": "https://wemeet-record-**********.file.myqcloud.com/cos/000011002000/4596186480641001928270/4596186480651001928270/a4dce3824c20fa8b77c3a8150cbfe87f.png/mid?sign=35f326091dd18ef603ee8a50f81286d3&t=1753779621",
        "video_url": "",
        "start_time": "*************",
        "end_time": "1748572995886",
        "duration": "2752576",
        "size": "80280978",
        "title": "AI-诺岚的快速会议",
        "description": "",
        "permission_type": 32,
        "password_switch": 0,
        "is_del": 0,
        "last_view_time": "0",
        "recorder_avatar": "https://meeting-75420.picgzc.qpic.cn/f074f9dc99c85e08120766ebe1ba5215c3e6e8344ccbabd4c68b7488b31b4f9b",
        "recorder_username": "AI-诺岚",
        "encode_record_id": "7ea63ee4-31b6-4316-898a-5a72767028a5",
        "meeting_info": {
          "subject": "AI-诺岚的快速会议",
          "meeting_code": "546898734",
          "meeting_id": "13064285038564104908",
          "meeting_code_mask": "40",
          "sub_meeting_id": "0"
        },
        "record_type": "cloud_record",
        "video_resolution_tag": "HD",
        "uni_record_id": "1928270459618648064",
        "record_state": 3,
        "video_width": 2160,
        "video_height": 1080,
        "jump_path": "/user-center/shared-record-middle?s=zL1Raw6CzmSigDRyUnG0cULRwkZZYrdyAYtx3If-6K4&from=0",
        "record_count": 2,
        "privilege_tips": "所有人可查看",
        "password": "",
        "share_url": "v2/cloud-record/share?id=7ea63ee4-31b6-4316-898a-5a72767028a5&from=3&is-single=false&record_type=2",
        "share_url_expired": "0",
        "meeting_type": 1,
        "file_state": 99,
        "is_rooms": 0,
        "viewer_status": 0,
        "file_id": "1928270459618648064",
        "hover_msg": {
          "msg_type": 1,
          "time_at": "*************"
        },
        "access_type": 0,
        "access_msg": "",
        "is_show_smart_tag": true,
        "role_type": 0,
        "activity_id": "",
        "activity_uid": "",
        "sharing_state": 0,
        "cover_cos_key": "",
        "jump_path_short": "",
        "share_url_short": "crm/lvm1Z7Gj4b",
        "jump_path_v2": "user-center/shared-record-middle?s=zL1Raw6CzmSigDRyUnG0cULRwkZZYrdyAYtx3If-6K4&from=0",
        "share_url_v2": "v2/cloud-record/share?id=7ea63ee4-31b6-4316-898a-5a72767028a5&from=3&is-single=false&record_type=2",
        "recorder_appid": "*********",
        "share_cover_url": "",
        "is_admin": false,
        "is_oversea": false,
        "preload_page": "middle-page",
        "share_scope": 1,
        "allow_delete": true,
        "allow_download": true
      }
    ],
    "current_time": "1753779621718",
    "page_info": {
      "index": "1",
      "size": "10",
      "count": "2",
      "has_more": 0
    }
  },
  "nonce": "9962f1f99e25fddad9e1fabb4938be23",
  "timestamp": 1753779621
}
```