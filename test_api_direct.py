#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
直接测试腾讯会议API - 使用您提供的代码
"""

import http.client
import json
import time
import urllib.parse
import gzip

def test_meeting_list_api():
    """
    直接测试会议列表API
    """
    print("🧪 直接测试腾讯会议API")
    print("=" * 60)
    
    try:
        conn = http.client.HTTPSConnection("meeting.tencent.com")

        payload = "{\"begin_time\":\"0\",\"end_time\":\"0\",\"meeting_code\":\"\",\"page_index\":1,\"page_size\":10,\"aggregationFastRecording\":0,\"cover_image_type\":\"meetlog_list_webp\",\"record_type_v4\":\"fast_record|cloud_record|user_upload|realtime_transcription|voice_record\",\"sort_by\":\"uni_record_id\",\"record_scene\":1}"

        headers = {
           'User-Agent': "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36",
  'Accept': "application/json, text/plain, */*",
  'Accept-Encoding': "gzip, deflate, br, zstd",
  'Content-Type': "application/json",
  'web-caller': "my_meetings",
  'sec-ch-ua-platform': "\"Windows\"",
  'sec-ch-ua': "\"Not)A;Brand\";v=\"8\", \"Chromium\";v=\"138\", \"Google Chrome\";v=\"138\"",
  'sec-ch-ua-mobile': "?0",
  'origin': "https://meeting.tencent.com",
  'sec-fetch-site': "same-origin",
  'sec-fetch-mode': "cors",
  'sec-fetch-dest': "empty",
  'referer': "https://meeting.tencent.com/user-center/meeting-record",
  'accept-language': "zh-CN,zh;q=0.9",
  'priority': "u=1, i",
  'Cookie': "_qimei_uuid42=18c100b2d08100a8a8fbf14c894cfed7d943272246; _qimei_fingerprint=9a1333709ea85a9b315d1eb15950c460; _qimei_q36=; _ga_RPMZTEBERQ=GS1.1.1735105631.2.1.1735105719.0.0.0; _qimei_i_3=60c15380920c53dcc593ad665a8425e6f1e9a7f5100d0b87b58a7e582490756564363e943989e2a0b38e; hy_source=web; _gcl_au=1.1.1022510828.1748236605; hy_user=6a66ee4e7c9f42c7bc51292a37abe266; hy_token=vpwRvqa5aJvKcNXYU2K0ThrCqgjRjqFhpgy1aUc+Yv0s9h/sR38bKUm8RXzCMqCueFj/CEC+qCHMEC/JSJFAJA==; _qimei_h38=7de5d7f5a8fbf14c894cfed70200000f31930a; _qimei_i_1=2de24d85c10f558fc6c1fb6153d221b6f6eaf7f91b5f50d4e1db2f582493206c616361963980e1dc87f1e4c0; qcloud_from=qcloud.baidu.seo-1752558156326; qcstats_seo_keywords=%E9%80%9A%E7%94%A8%E6%8A%80%E6%9C%AF-%E7%BC%96%E7%A8%8B%E8%AF%AD%E8%A8%80-python%2C%E4%BA%91%E4%BA%A7%E5%93%81-%E5%AE%B9%E5%99%A8%E4%B8%8E%E4%B8%AD%E9%97%B4%E4%BB%B6-%E6%97%A5%E5%BF%97; sensorsdata2015jssdkcross=%7B%22distinct_id%22%3A%22100009325222%22%2C%22first_id%22%3A%22194f4284b9afe2-05e3105d450a0b-26011951-4953600-194f4284b9b2700%22%2C%22props%22%3A%7B%22%24latest_traffic_source_type%22%3A%22%E8%87%AA%E7%84%B6%E6%90%9C%E7%B4%A2%E6%B5%81%E9%87%8F%22%2C%22%24latest_utm_medium%22%3A%22cpc%22%2C%22%24search_keyword_id%22%3A%22f12eedaf0177fc23000000066881d78f%22%2C%22%24search_keyword_id_type%22%3A%22baidu_seo_keyword_id%22%2C%22%24search_keyword_id_hash%22%3A6719589322102133%7D%2C%22identities%22%3A%22eyIkaWRlbnRpdHlfY29va2llX2lkIjoiMTk0ZjQyODRiOWFmZTItMDVlMzEwNWQ0NTBhMGItMjYwMTE5NTEtNDk1MzYwMC0xOTRmNDI4NGI5YjI3MDAiLCIkaWRlbnRpdHlfbG9naW5faWQiOiIxMDAwMDkzMjUyMjIifQ%3D%3D%22%2C%22history_login_id%22%3A%7B%22name%22%3A%22%24identity_login_id%22%2C%22value%22%3A%22100009325222%22%7D%2C%22%24device_id%22%3A%22194f4284b9afe2-05e3105d450a0b-26011951-4953600-194f4284b9b2700%22%7D; we_meet_token=eJxUkV9vmzAUxb*LX7tMBtv8ibSHtWoyNRlpm5DSJ2TsC3ghQGxD0k777lMoUVPe*J1zbZ97-qLNcv1d1cbyWkCqJJoihr4NlLdt2g3EodRxmEuYg30ceoSQ0WLkLj3bBpeLPz5nFOHUKg0pzy1oNEUepgHGo9aDNqqphymX4sCl58lRzDqjajAGTdERshFatYfzU3xGfB-jIBx51RSqTu1bC4NdlNyOioReXTKNSEPxcWvgXRKoAk3R0w33CY7L4HVv2IKefonTjJyCZnXzclw1L63qG-JUwUPExbyNN9s5XxzrO-4uoHz0wnIWkdf1c-W*qkm0s-ywvd*6*8rwRZu85QUnh75hsWSwXJf8zwISmsT1LNIPXVLeHfvdRs2y6Pk2SnyNs8SqW7YMq9Ar-MfwEN93y7z-PZe9dW2e-Nz9uFr9dbSD2oMi3idodSM7Yb*YzPBDHSKoCPGE5iSf0ACTCQcXJhlImbsez5j8XI8k*uoALkTT1TYVjR5b9yjzmOPTSx*dAX2pw0H--gcAAP--PTK3Dw__; account_corp_id=*********; corp_id=*********; app_uid=144115235107096333; token_expire_time=**********; user_type=1; ACTIVITY_TICKET=eJxMkl1zojwcxb9Lbn3mMSEvgDN7sVZ3XaxF61axN0wgESPy0hCwdGe-%2A04ROubuf-45c05%2Akz-g9%2AP2fx7HRZ2b0LSlBBMA-utUJWRu1FFJDSbAgreDQkQIQtTCFEEbugxj3N-nZalEyE2ItQATAHu5EmnYrcAEIIhs23W-LPK9VFqG-Gi6EOTAwdVIXaki75ItAh2LfKb3S6Oyz57Ipti2IXTcXr8UicqHV1xlfOJmKKESMAFp8xL8XMzWeqcsX8Sb18P0PWuWlzjw9stR6nuL-dQr6m2iL3wusrympTT6IXBKfxNtfvl4ObtQz3uy2khoOD17LVnt34qqDvIzfzDZEz9F19ePlxVtZ-tD5o2r0TPbW%2AlptV40y8MuiPicUJaMk-FouYsif75dXdfPfrt1S8f7vuDpY7ypfpzPcvWtL65lcsPQz0I2KpZhR-MO8P1silTm4Zcx%2Aej1N5VJhdmdsXNZjB2xIzkTWBJGkYOYdI9YMiEoiejAfPgjcaHLWxwjlFFkk4F%2AXUk9wEfg778AAAD--w%2AAtUg_; lz_sign=KPcEAexvXPDfrJdvCsbqrj9rNhkVdwsj5lLzFVuyPzHm585djl2z7EF7FVkZIVUPT_gnmwuq5SInu8GvrMW3kWdBvPVaa17VxiYYAJ4-Yf0; lz_appid=*********; lz_uid=144115235107096333; lz_time=*************; landing_url=https://meeting.tencent.com/ct/24g6w13866; landing_path=https://meeting.tencent.com/ct/24g6w13866; landing_referralurl=; landing_referraldomain=; web_uid=97ce1f84-3627-47a8-b446-cc451a773ba2; _gid=GA1.2.274803522.1753770096; _gat_UA-205111495-1=1; lz_expire=1753788098946; _ga=GA1.2.1561455755.1739263069; _ga_6WSZ0YS5ZQ=GS2.1.s1753770095$o1$g1$t1753770139$j16$l0$h0"
        }

        # 构建URL参数
        current_timestamp = int(time.time() * 1000)
        url_params = {
            'c_app_id': '',
            'c_os_model': 'web',
            'c_os': 'web',
            'c_os_version': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36',
            'c_timestamp': str(current_timestamp),
            'c_nonce': 'zwPrMxGEj',
            'c_app_version': '',
            'c_instance_id': '5',
            'c_account_corp_id': '*********',
            'rnds': 'zwPrMxGEj',
            'c_app_uid': '',
            'c_district': '0',
            'trace-id': 'd0ce92aa72c87cb50c49751531e63461',
            'c_lang': 'zh-CN'
        }

        url_query = urllib.parse.urlencode(url_params)
        full_url = f"/wemeet-tapi/v2/meetlog/dashboard/my-record-list?{url_query}"

        print("📡 发送API请求...")
        print(f"🔗 请求URL: https://meeting.tencent.com{full_url}")
        print(f"📝 请求方法: POST")
        print(f"📦 请求体长度: {len(payload)} 字符")
        print(f"🍪 Cookie长度: {len(headers.get('Cookie', ''))} 字符")
        print(f"⏰ 时间戳: {current_timestamp}")

        # 显示关键请求头
        print("📋 关键请求头:")
        for key in ['User-Agent', 'Content-Type', 'origin', 'referer']:
            if key in headers:
                print(f"   {key}: {headers[key]}")

        print("📋 Cookie前100字符:")
        cookie = headers.get('Cookie', '')
        print(f"   {cookie[:100]}...")

        conn.request("POST", full_url, payload, headers)

        res = conn.getresponse()
        data = res.read()
        
        print(f"📊 响应状态码: {res.status}")
        print(f"📊 响应头: {dict(res.getheaders())}")
        print(f"📄 响应内容长度: {len(data)} 字节")

        # 检查是否是gzip压缩
        content_encoding = res.getheader('Content-Encoding', '')
        if content_encoding == 'gzip':
            print("🗜️ 响应内容是gzip压缩，正在解压...")
            try:
                response_text = gzip.decompress(data).decode("utf-8")
                print(f"📄 解压后内容长度: {len(response_text)} 字符")
            except Exception as decompress_error:
                print(f"❌ gzip解压失败: {str(decompress_error)}")
                return False, None
        else:
            response_text = data.decode("utf-8")

        print(f"📄 响应内容前200字符: {response_text[:200]}...")

        # 尝试解析JSON
        try:
            response_json = json.loads(response_text)
            print("✅ JSON解析成功")
            
            # 检查响应结构
            if "code" in response_json:
                code = response_json.get("code")
                msg = response_json.get("msg", "")
                print(f"📋 响应代码: {code}")
                print(f"📋 响应消息: {msg}")
                
                if code == 0:
                    print("✅ API调用成功")
                    
                    # 检查数据
                    data_section = response_json.get("data", {})
                    records = data_section.get("records", [])
                    print(f"📊 找到 {len(records)} 个会议记录")
                    
                    # 显示前几个会议
                    for i, meeting in enumerate(records[:3], 1):
                        title = meeting.get("title", "未知会议")
                        start_time = meeting.get("start_time", "")
                        record_type = meeting.get("record_type", "")
                        print(f"  {i}. {title}")
                        print(f"     开始时间: {start_time}")
                        print(f"     记录类型: {record_type}")
                    
                    return True, records
                else:
                    print(f"❌ API返回错误: {code} - {msg}")
                    return False, None
            else:
                print("⚠️ 响应格式异常，没有找到code字段")
                print(f"📄 响应内容: {response_text[:500]}...")
                return False, None
                
        except json.JSONDecodeError as e:
            print(f"❌ JSON解析失败: {str(e)}")
            print(f"📄 响应内容: {response_text[:500]}...")
            return False, None
            
    except Exception as e:
        print(f"❌ API请求失败: {str(e)}")
        return False, None
    finally:
        try:
            conn.close()
        except:
            pass

def main():
    """
    主函数
    """
    print("🧪 腾讯会议API直接测试")
    print("使用您提供的确认可用的代码进行测试")
    print("=" * 60)
    
    success, meetings = test_meeting_list_api()
    
    if success and meetings:
        print(f"\n✅ 测试成功！找到 {len(meetings)} 个会议记录")
        print("\n💡 现在可以使用下载器进行文件下载")
        
        # 显示目标会议信息
        target_start_time = "1748570479000"
        target_meeting = None
        for meeting in meetings:
            if meeting.get("start_time") == target_start_time:
                target_meeting = meeting
                break
        
        if target_meeting:
            print(f"\n🎯 找到目标会议 (开始时间: {target_start_time}):")
            print(f"   标题: {target_meeting.get('title')}")
            print(f"   记录类型: {target_meeting.get('record_type')}")
            print(f"   记录ID: {target_meeting.get('record_id')}")
            print(f"   唯一记录ID: {target_meeting.get('uni_record_id')}")
        else:
            print(f"\n⚠️ 未找到开始时间为 {target_start_time} 的会议")
            
    else:
        print("\n❌ 测试失败")

if __name__ == "__main__":
    main()
