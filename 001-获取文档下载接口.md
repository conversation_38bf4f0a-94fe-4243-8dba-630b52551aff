```python
import http.client

conn = http.client.HTTPSConnection("meeting.tencent.com")

headers = {
  'User-Agent': "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/130.0.0.0 Safari/537.36",
  'Accept': "application/json, text/plain, */*",
  'Accept-Encoding': "gzip, deflate, br, zstd",
  'sec-ch-ua-platform': "\"Windows\"",
  'sec-ch-ua': "\"Chromium\";v=\"130\", \"Google Chrome\";v=\"130\", \"Not?A_Brand\";v=\"99\"",
  'sec-ch-ua-mobile': "?0",
  'sec-fetch-site': "same-origin",
  'sec-fetch-mode': "cors",
  'sec-fetch-dest': "empty",
  'referer': "https://meeting.tencent.com/",
  'accept-language': "zh-C<PERSON>,zh;q=0.9",
  'priority': "u=1, i",
  'Cookie': "_qimei_uuid42=18c100b2d08100a8a8fbf14c894cfed7d943272246; _qimei_fingerprint=9a1333709ea85a9b315d1eb15950c460; _qimei_q36=; _ga_RPMZTEBERQ=GS1.1.1735105631.2.1.1735105719.0.0.0; _qimei_i_3=60c15380920c53dcc593ad665a8425e6f1e9a7f5100d0b87b58a7e582490756564363e943989e2a0b38e; hy_source=web; _gcl_au=1.1.1022510828.1748236605; hy_user=6a66ee4e7c9f42c7bc51292a37abe266; hy_token=vpwRvqa5aJvKcNXYU2K0ThrCqgjRjqFhpgy1aUc+Yv0s9h/sR38bKUm8RXzCMqCueFj/CEC+qCHMEC/JSJFAJA==; _qimei_h38=7de5d7f5a8fbf14c894cfed70200000f31930a; _qimei_i_1=2de24d85c10f558fc6c1fb6153d221b6f6eaf7f91b5f50d4e1db2f582493206c616361963980e1dc87f1e4c0; qcloud_from=qcloud.baidu.seo-1752558156326; qcstats_seo_keywords=%E9%80%9A%E7%94%A8%E6%8A%80%E6%9C%AF-%E7%BC%96%E7%A8%8B%E8%AF%AD%E8%A8%80-python%2C%E4%BA%91%E4%BA%A7%E5%93%81-%E5%AE%B9%E5%99%A8%E4%B8%8E%E4%B8%AD%E9%97%B4%E4%BB%B6-%E6%97%A5%E5%BF%97; sensorsdata2015jssdkcross=%7B%22distinct_id%22%3A%22100009325222%22%2C%22first_id%22%3A%22194f4284b9afe2-05e3105d450a0b-26011951-4953600-194f4284b9b2700%22%2C%22props%22%3A%7B%22%24latest_traffic_source_type%22%3A%22%E8%87%AA%E7%84%B6%E6%90%9C%E7%B4%A2%E6%B5%81%E9%87%8F%22%2C%22%24latest_utm_medium%22%3A%22cpc%22%2C%22%24search_keyword_id%22%3A%22f12eedaf0177fc23000000066881d78f%22%2C%22%24search_keyword_id_type%22%3A%22baidu_seo_keyword_id%22%2C%22%24search_keyword_id_hash%22%3A6719589322102133%7D%2C%22identities%22%3A%22eyIkaWRlbnRpdHlfY29va2llX2lkIjoiMTk0ZjQyODRiOWFmZTItMDVlMzEwNWQ0NTBhMGItMjYwMTE5NTEtNDk1MzYwMC0xOTRmNDI4NGI5YjI3MDAiLCIkaWRlbnRpdHlfbG9naW5faWQiOiIxMDAwMDkzMjUyMjIifQ%3D%3D%22%2C%22history_login_id%22%3A%7B%22name%22%3A%22%24identity_login_id%22%2C%22value%22%3A%22100009325222%22%7D%2C%22%24device_id%22%3A%22194f4284b9afe2-05e3105d450a0b-26011951-4953600-194f4284b9b2700%22%7D; we_meet_token=eJxUkV9vmzAUxb*LX7tMBtv8ibSHtWoyNRlpm5DSJ2TsC3ghQGxD0k777lMoUVPe*J1zbZ97-qLNcv1d1cbyWkCqJJoihr4NlLdt2g3EodRxmEuYg30ceoSQ0WLkLj3bBpeLPz5nFOHUKg0pzy1oNEUepgHGo9aDNqqphymX4sCl58lRzDqjajAGTdERshFatYfzU3xGfB-jIBx51RSqTu1bC4NdlNyOioReXTKNSEPxcWvgXRKoAk3R0w33CY7L4HVv2IKefonTjJyCZnXzclw1L63qG-JUwUPExbyNN9s5XxzrO-4uoHz0wnIWkdf1c-W*qkm0s-ywvd*6*8rwRZu85QUnh75hsWSwXJf8zwISmsT1LNIPXVLeHfvdRs2y6Pk2SnyNs8SqW7YMq9Ar-MfwEN93y7z-PZe9dW2e-Nz9uFr9dbSD2oMi3idodSM7Yb*YzPBDHSKoCPGE5iSf0ACTCQcXJhlImbsez5j8XI8k*uoALkTT1TYVjR5b9yjzmOPTSx*dAX2pw0H--gcAAP--PTK3Dw__; account_corp_id=*********; corp_id=*********; app_uid=144115235107096333; token_expire_time=**********; user_type=1; ACTIVITY_TICKET=eJxMkl1zojwcxb9Lbn3mMSEvgDN7sVZ3XaxF61axN0wgESPy0hCwdGe-%2A04ROubuf-45c05%2Akz-g9%2AP2fx7HRZ2b0LSlBBMA-utUJWRu1FFJDSbAgreDQkQIQtTCFEEbugxj3N-nZalEyE2ItQATAHu5EmnYrcAEIIhs23W-LPK9VFqG-Gi6EOTAwdVIXaki75ItAh2LfKb3S6Oyz57Ipti2IXTcXr8UicqHV1xlfOJmKKESMAFp8xL8XMzWeqcsX8Sb18P0PWuWlzjw9stR6nuL-dQr6m2iL3wusrympTT6IXBKfxNtfvl4ObtQz3uy2khoOD17LVnt34qqDvIzfzDZEz9F19ePlxVtZ-tD5o2r0TPbW%2AlptV40y8MuiPicUJaMk-FouYsif75dXdfPfrt1S8f7vuDpY7ypfpzPcvWtL65lcsPQz0I2KpZhR-MO8P1silTm4Zcx%2Aej1N5VJhdmdsXNZjB2xIzkTWBJGkYOYdI9YMiEoiejAfPgjcaHLWxwjlFFkk4F%2AXUk9wEfg778AAAD--w%2AAtUg_; lz_sign=KPcEAexvXPDfrJdvCsbqrj9rNhkVdwsj5lLzFVuyPzHm585djl2z7EF7FVkZIVUPT_gnmwuq5SInu8GvrMW3kWdBvPVaa17VxiYYAJ4-Yf0; lz_appid=*********; lz_uid=144115235107096333; lz_time=*************; landing_url=https://meeting.tencent.com/ct/24g6w13866; landing_path=https://meeting.tencent.com/ct/24g6w13866; landing_referralurl=; landing_referraldomain=; web_uid=97ce1f84-3627-47a8-b446-cc451a773ba2; _gid=GA1.2.274803522.1753770096; _ga=GA1.2.**********.**********; _ga_6WSZ0YS5ZQ=GS2.1.s1753778081$o2$g0$t1753778083$j58$l0$h0; lz_expire=*************"
}

conn.request("GET", "/wemeet-cloudrecording-webapi/v1/minutes/export_by_meeting?c_app_id=&c_os_model=web&c_os=web&c_os_version=Mozilla%2F5.0+%28Windows+NT+10.0%3B+Win64%3B+x64%29+AppleWebKit%2F537.36+%28KHTML%2C+like+Gecko%29+Chrome%2F130.0.0.0+Safari%2F537.36&c_timestamp=*************&c_nonce=pEerF8Jxr&c_app_version=&c_instance_id=5&rnds=pEerF8Jxr&c_district=0&platform=Web&c_app_uid=&c_account_corp_id=*********&trace-id=d1f97ea5b4669aa698bbfd884d582c76&meeting_id=13064285038564104908&recording_id=&lang=zh&type=word&uniq_meeting_id=1928270459618648064&tk=&id=7ea63ee4-31b6-4316-898a-5a72767028a5&pwd=&activity_uid=&page_source=record&minutes_version=1&host=&from_share=1&enter_from=share&c_lang=zh-CN", headers=headers)

res = conn.getresponse()
data = res.read()

print(data.decode("utf-8"))
```


以上请求参数，type=word 就是文档类型，可以是pdf,word,txt 格式。

返回数据：
```json
{
  "code": 0,
  "nonce": "4384d0c11361e1ad0a9b8e3efea0acb7",
  "urls": [
    "https://wemeet-minutes-prod-**********.cos.ap-guangzhou.myqcloud.com/%2Fexport-minutes-file/13064285038564104908/1928270459618648065-zh-minutes-all-1-v2.docx?x-cos-security-token=D3iUWqpLBeSgLrMBIbiwdvUByJocVQqae34c96410d32c77139803710e582238emhNLCeSV0ZH1Po4UcJFeiZbnX_OL_wD2VxGPrdQ06r3pKgD8fZnUAy7mM5q8qSGOZ9M3tC6ZThaNnqh_poUgAvdy7Nz-xhVUwFMw1Qjsz5tokRZez0aTvlZ-QRsT-MVN8e-EC-tJGDnn4phNFfrlV6qKQkapRJrJjcwix7WnHUGRBLaXg8W0zPMM7esv4iipcIrlQDFqtvXgIZYRiv82DO0q3zpAX9tZl-rpUAkJE8ETmW8en-Gc07qeE65CAzMRJRJohQLweaQHl31L8jP05LGLfMjsQbpiT4Fpb6HNvs1IaorVjCXVlvq5uV3rl0T1Vi_VTDdX_b-JJGMHqMrXnMvstcg-TyrV0Jmvk6MaVu_pwxqoH9AX3vRIMu5XuCJq2E-agGbOK-gSVQtvntyCEoRfSLnF5nGYybwFsJoO9AZe0pD_5J9-oE3PpInl95ZnAFmiMKP7nSj6Yez5HtUBhpdxCm92yvz0lvgXWXgg17vyTNHYdF3zaOtybwlZgUDenVIzf5oJOdqv_NKy4ixGSfo_nKLz0kY0LmjQg7WE4Y9u_hz54x-VhJk9vnUQqza3qnOGbQtRoTrCx15xJaOnDl_oXB0mWuYRQ2qxu_tMTlKrxY9CaS1McXpzfQe93qv7kGEq8sOEIiWbQjUkUxZPnRiXPir6VwXm1Hi35PU5sKXd0r3j1izTW4XUOO3cjQVjw6tC5LdEjDHzy9vuT7J9VVILUUcxkpVvx_nw0PZnEC8T8xJ-lYHm4oq6afVUzqECXH2mY7aSSw6qnQUJtRclf5qPZxg8TpcvGNpeXrl0i-OshPYoGa4v17P0SfZbEy-cms3rWUNe7lgs8kAb4Ym1WqwZJsnBMypYZaW40qXZDKIPW5WS-TaM359wZr2Xms0mTpYd70LfGPWMyrwn_AC5Lg&q-sign-algorithm=sha1&q-ak=AKID6UJuPDA3lhO1VTV7I0BZsi7Rl-A_RBt7n95k57LQhCP2baAUC4IgIJ4QeDQkNOac&q-sign-time=1753780421%3B1753784021&q-key-time=1753780421%3B1753784021&q-header-list=host&q-url-param-list=x-cos-security-token&q-signature=12f078ae364840d6a4713c974bc0bc5fc36dab7b",
    "https://wemeet-minutes-prod-**********.cos.ap-guangzhou.myqcloud.com/%2Fexport-minutes-file/13064285038564104908/1928281515270578177-zh-minutes-all-1-v2.docx?x-cos-security-token=D3iUWqpLBeSgLrMBIbiwdvUByJocVQqa6754551b6757c2446536c152a075ff20mhNLCeSV0ZH1Po4UcJFeiSK-GuEzwvrCRZXzX2bMRITwBzeNaLFZF5d1q3DbqnatevqPhM6W3ZIHnqfC-n2X_iYZpzgNgVbmlxCRELsWhSh5tP57uTqSP11npvHl5YUOkZ5Ef6VXfE0VfPr5Y4DEa8LepLc98nNUzE4JHB-Y3KyNsUYhQG12xU84Wh7zfHfgIwaT844XQf408soKYbTtJBE-79MQhKNm_lFVp5K-0w-Vu52uGhMPIiqPn5ygfw48o7EXpxdHVtcPDyyobtUznJ5WT21aEzoqiq3bj2L1cOPAC_spcurUC2WI0dZP06noN4hItl8BTUe_WAmupyT8BT9e2g41QLIBRpLIAxsFLmmKoL0jYLWkbKt5odojmldetX2DAyfHw_Gf9sABeIMIUloBDJLCTdpgPRUomKkY1wGvixGISjGsJf9LNSQu0E6iI2j7tFDYO-Zr50Wl7_7nL5es3zxF3HVfp6BcaK8be6Jz3dDE4W_UtVasV9f312xhngDYR4FYEddwrzRtqqs4NiFLmfPDsmb2kZiCSuVkBZW9IyyJC17ORdSREx9nPBbQhwc9h-qGfgS5HEE0lMCxoNzzHD_AIySCqAfMxaQiZDVFTndjKN_9f9yfyNjhrsoKzfkpOfGNwgqZLmYScYG8bgNGlolt0x0fnlxgjMxMwzbps32zqLqmh6leRTRd_ICIgFTY3tAfXTQL6ABUHL9ngnQRWWKRebYuqaG_pqQLHE9_9x1jGg_3vxZQp8PDmEJpVgYkurpD5VpuxIwBXFiBy8LvRXUKe1XgEfSALKeC5kpvfdQw8b5jFeduJhWDxtd1tY6jM-qrg7Op7V5ppRm4xWulYOJv0nGBpyCyEdBP5DSja92Yn-xLa-1-VwOQq6i_uYQXgeCGBJqEyDh1aVA44A&q-sign-algorithm=sha1&q-ak=AKID8IdKXE_2QcJNonyAXk0X58Hxj6sq_AsJeEvGFvjQ0izfiqqqeAqGbODGjAGIPwP2&q-sign-time=1753780422%3B1753784022&q-key-time=1753780422%3B1753784022&q-header-list=host&q-url-param-list=x-cos-security-token&q-signature=af7166ee2094e4cf0e4f93f787dac6d53a22a98a"
  ]
}
```
