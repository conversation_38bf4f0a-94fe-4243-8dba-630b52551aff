#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
腾讯会议文件下载器
支持下载视频、音频、文档（Word、PDF、TXT）等文件
按会议开始时间创建目录并规范命名
"""

import http.client
import json
import os
import urllib.request
import urllib.parse
from datetime import datetime
import time
import re
import logging
import gzip

class TencentMeetingDownloader:
    def __init__(self, cookie):
        """
        初始化下载器
        :param cookie: 从浏览器获取的Cookie字符串
        """
        self.cookie = cookie
        self.headers = {
 'User-Agent': "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36",
  'Accept': "application/json, text/plain, */*",
  'Accept-Encoding': "gzip, deflate, br, zstd",
  'Content-Type': "application/json",
  'web-caller': "my_meetings",
  'sec-ch-ua-platform': "\"Windows\"",
  'sec-ch-ua': "\"Not)A;Brand\";v=\"8\", \"Chromium\";v=\"138\", \"Google Chrome\";v=\"138\"",
  'sec-ch-ua-mobile': "?0",
  'origin': "https://meeting.tencent.com",
  'sec-fetch-site': "same-origin",
  'sec-fetch-mode': "cors",
  'sec-fetch-dest': "empty",
  'referer': "https://meeting.tencent.com/user-center/meeting-record",
  'accept-language': "zh-CN,zh;q=0.9",
  'priority': "u=1, i",
  'Cookie': "_qimei_uuid42=18c100b2d08100a8a8fbf14c894cfed7d943272246; _qimei_fingerprint=9a1333709ea85a9b315d1eb15950c460; _qimei_q36=; _ga_RPMZTEBERQ=GS1.1.1735105631.2.1.1735105719.0.0.0; _qimei_i_3=60c15380920c53dcc593ad665a8425e6f1e9a7f5100d0b87b58a7e582490756564363e943989e2a0b38e; hy_source=web; _gcl_au=1.1.1022510828.1748236605; hy_user=6a66ee4e7c9f42c7bc51292a37abe266; hy_token=vpwRvqa5aJvKcNXYU2K0ThrCqgjRjqFhpgy1aUc+Yv0s9h/sR38bKUm8RXzCMqCueFj/CEC+qCHMEC/JSJFAJA==; _qimei_h38=7de5d7f5a8fbf14c894cfed70200000f31930a; _qimei_i_1=2de24d85c10f558fc6c1fb6153d221b6f6eaf7f91b5f50d4e1db2f582493206c616361963980e1dc87f1e4c0; qcloud_from=qcloud.baidu.seo-1752558156326; qcstats_seo_keywords=%E9%80%9A%E7%94%A8%E6%8A%80%E6%9C%AF-%E7%BC%96%E7%A8%8B%E8%AF%AD%E8%A8%80-python%2C%E4%BA%91%E4%BA%A7%E5%93%81-%E5%AE%B9%E5%99%A8%E4%B8%8E%E4%B8%AD%E9%97%B4%E4%BB%B6-%E6%97%A5%E5%BF%97; sensorsdata2015jssdkcross=%7B%22distinct_id%22%3A%22100009325222%22%2C%22first_id%22%3A%22194f4284b9afe2-05e3105d450a0b-26011951-4953600-194f4284b9b2700%22%2C%22props%22%3A%7B%22%24latest_traffic_source_type%22%3A%22%E8%87%AA%E7%84%B6%E6%90%9C%E7%B4%A2%E6%B5%81%E9%87%8F%22%2C%22%24latest_utm_medium%22%3A%22cpc%22%2C%22%24search_keyword_id%22%3A%22f12eedaf0177fc23000000066881d78f%22%2C%22%24search_keyword_id_type%22%3A%22baidu_seo_keyword_id%22%2C%22%24search_keyword_id_hash%22%3A6719589322102133%7D%2C%22identities%22%3A%22eyIkaWRlbnRpdHlfY29va2llX2lkIjoiMTk0ZjQyODRiOWFmZTItMDVlMzEwNWQ0NTBhMGItMjYwMTE5NTEtNDk1MzYwMC0xOTRmNDI4NGI5YjI3MDAiLCIkaWRlbnRpdHlfbG9naW5faWQiOiIxMDAwMDkzMjUyMjIifQ%3D%3D%22%2C%22history_login_id%22%3A%7B%22name%22%3A%22%24identity_login_id%22%2C%22value%22%3A%22100009325222%22%7D%2C%22%24device_id%22%3A%22194f4284b9afe2-05e3105d450a0b-26011951-4953600-194f4284b9b2700%22%7D; we_meet_token=eJxUkV9vmzAUxb*LX7tMBtv8ibSHtWoyNRlpm5DSJ2TsC3ghQGxD0k777lMoUVPe*J1zbZ97-qLNcv1d1cbyWkCqJJoihr4NlLdt2g3EodRxmEuYg30ceoSQ0WLkLj3bBpeLPz5nFOHUKg0pzy1oNEUepgHGo9aDNqqphymX4sCl58lRzDqjajAGTdERshFatYfzU3xGfB-jIBx51RSqTu1bC4NdlNyOioReXTKNSEPxcWvgXRKoAk3R0w33CY7L4HVv2IKefonTjJyCZnXzclw1L63qG-JUwUPExbyNN9s5XxzrO-4uoHz0wnIWkdf1c-W*qkm0s-ywvd*6*8rwRZu85QUnh75hsWSwXJf8zwISmsT1LNIPXVLeHfvdRs2y6Pk2SnyNs8SqW7YMq9Ar-MfwEN93y7z-PZe9dW2e-Nz9uFr9dbSD2oMi3idodSM7Yb*YzPBDHSKoCPGE5iSf0ACTCQcXJhlImbsez5j8XI8k*uoALkTT1TYVjR5b9yjzmOPTSx*dAX2pw0H--gcAAP--PTK3Dw__; account_corp_id=*********; corp_id=*********; app_uid=144115235107096333; token_expire_time=**********; user_type=1; ACTIVITY_TICKET=eJxMkl1zojwcxb9Lbn3mMSEvgDN7sVZ3XaxF61axN0wgESPy0hCwdGe-%2A04ROubuf-45c05%2Akz-g9%2AP2fx7HRZ2b0LSlBBMA-utUJWRu1FFJDSbAgreDQkQIQtTCFEEbugxj3N-nZalEyE2ItQATAHu5EmnYrcAEIIhs23W-LPK9VFqG-Gi6EOTAwdVIXaki75ItAh2LfKb3S6Oyz57Ipti2IXTcXr8UicqHV1xlfOJmKKESMAFp8xL8XMzWeqcsX8Sb18P0PWuWlzjw9stR6nuL-dQr6m2iL3wusrympTT6IXBKfxNtfvl4ObtQz3uy2khoOD17LVnt34qqDvIzfzDZEz9F19ePlxVtZ-tD5o2r0TPbW%2AlptV40y8MuiPicUJaMk-FouYsif75dXdfPfrt1S8f7vuDpY7ypfpzPcvWtL65lcsPQz0I2KpZhR-MO8P1silTm4Zcx%2Aej1N5VJhdmdsXNZjB2xIzkTWBJGkYOYdI9YMiEoiejAfPgjcaHLWxwjlFFkk4F%2AXUk9wEfg778AAAD--w%2AAtUg_; lz_sign=KPcEAexvXPDfrJdvCsbqrj9rNhkVdwsj5lLzFVuyPzHm585djl2z7EF7FVkZIVUPT_gnmwuq5SInu8GvrMW3kWdBvPVaa17VxiYYAJ4-Yf0; lz_appid=*********; lz_uid=144115235107096333; lz_time=*************; landing_url=https://meeting.tencent.com/ct/24g6w13866; landing_path=https://meeting.tencent.com/ct/24g6w13866; landing_referralurl=; landing_referraldomain=; web_uid=97ce1f84-3627-47a8-b446-cc451a773ba2; _gid=GA1.2.274803522.1753770096; _gat_UA-205111495-1=1; lz_expire=1753788098946; _ga=GA1.2.1561455755.1739263069; _ga_6WSZ0YS5ZQ=GS2.1.s1753770095$o1$g1$t1753770139$j16$l0$h0"
        }
        
    def get_meeting_list(self, page_size=10):
        """
        获取会议列表
        :param page_size: 每页数量
        :return: 会议列表数据
        """
        try:
            print("🔗 开始获取会议列表...")
            conn = http.client.HTTPSConnection("meeting.tencent.com")

            payload = json.dumps({
                "begin_time": "0",
                "end_time": "0",
                "meeting_code": "",
                "page_index": 1,
                "page_size": page_size,
                "aggregationFastRecording": 0,
                "cover_image_type": "meetlog_list_webp",
                "record_type_v4": "fast_record|cloud_record|user_upload|realtime_transcription|voice_record",
                "sort_by": "uni_record_id",
                "record_scene": 1
            })

            # 构建请求URL（需要根据实际情况调整参数）
            timestamp = int(time.time() * 1000)
            url = f"/wemeet-tapi/v2/meetlog/dashboard/my-record-list?c_app_id=&c_os_model=web&c_os=web&c_os_version=Mozilla%2F5.0+%28Windows+NT+10.0%3B+Win64%3B+x64%29+AppleWebKit%2F537.36+%28KHTML%2C+like+Gecko%29+Chrome%2F*********+Safari%2F537.36&c_timestamp={timestamp}&c_nonce=zwPrMxGEj&c_app_version=&c_instance_id=5&c_account_corp_id=*********&rnds=zwPrMxGEj&c_app_uid=&c_district=0&trace-id=d0ce92aa72c87cb50c49751531e63461&c_lang=zh-CN"

            print(f"📡 请求URL: https://meeting.tencent.com{url}")
            print(f"📦 请求体: {payload}")
            print(f"🍪 Cookie长度: {len(self.headers.get('Cookie', ''))} 字符")
            print(f"🍪 Cookie前100字符: {self.headers.get('Cookie', '')[:100]}...")
            print(f"⏰ 时间戳: {timestamp}")

            # 显示关键请求头
            print("📋 关键请求头:")
            for key in ['User-Agent', 'Content-Type', 'origin', 'referer', 'web-caller']:
                if key in self.headers:
                    print(f"   {key}: {self.headers[key]}")

            conn.request("POST", url, payload, self.headers)
            res = conn.getresponse()
            data = res.read()

            print(f"📊 响应状态码: {res.status}")
            print(f"📊 响应头: {dict(res.getheaders())}")
            print(f"📄 响应内容长度: {len(data)} 字节")

            if len(data) > 0:
                try:
                    # 检查是否是gzip压缩
                    content_encoding = res.getheader('Content-Encoding', '')
                    if content_encoding == 'gzip':
                        print("🗜️ 响应内容是gzip压缩，正在解压...")
                        decoded_data = gzip.decompress(data).decode("utf-8")
                        print(f"📄 解压后内容长度: {len(decoded_data)} 字符")
                    else:
                        decoded_data = data.decode("utf-8")

                    print(f"📄 响应内容前200字符: {decoded_data[:200]}...")
                    result = json.loads(decoded_data)
                except Exception as decode_error:
                    print(f"❌ 解码响应失败: {str(decode_error)}")
                    print(f"📄 原始响应: {data}")
                    return []
            else:
                print("📄 响应内容为空")
                return []

            conn.close()

            if result.get("code") == 0:
                records = result.get("data", {}).get("records", [])
                print(f"✅ 成功获取 {len(records)} 个会议记录")
                return records
            else:
                error_msg = result.get('msg', '未知错误')
                error_code = result.get('code', 'unknown')
                print(f"❌ 获取会议列表失败: 错误代码={error_code}, 错误信息={error_msg}")
                print(f"📄 完整响应: {result}")
                return []

        except Exception as e:
            print(f"❌ 获取会议列表异常: {str(e)}")
            print(f"❌ 异常类型: {type(e).__name__}")
            import traceback
            print(f"❌ 异常堆栈: {traceback.format_exc()}")
            return []
    
    def get_document_download_urls(self, meeting_id, record_id, uni_record_id, doc_types=None):
        """
        获取文档下载链接
        :param meeting_id: 会议ID
        :param record_id: 记录ID
        :param uni_record_id: 唯一记录ID
        :param doc_types: 文档类型列表，如['word', 'pdf', 'txt']
        :return: 文档下载链接字典
        """
        if doc_types is None:
            doc_types = ['word', 'pdf', 'txt']
            
        document_urls = {}
        
        for doc_type in doc_types:
            try:
                conn = http.client.HTTPSConnection("meeting.tencent.com")
                
                timestamp = int(time.time() * 1000)
                url = f"/wemeet-cloudrecording-webapi/v1/minutes/export_by_meeting?c_app_id=&c_os_model=web&c_os=web&c_os_version=Mozilla%2F5.0+%28Windows+NT+10.0%3B+Win64%3B+x64%29+AppleWebKit%2F537.36+%28KHTML%2C+like+Gecko%29+Chrome%2F130.0.0.0+Safari%2F537.36&c_timestamp={timestamp}&c_nonce=pEerF8Jxr&c_app_version=&c_instance_id=5&rnds=pEerF8Jxr&c_district=0&platform=Web&c_app_uid=&c_account_corp_id=*********&trace-id=d1f97ea5b4669aa698bbfd884d582c76&meeting_id={meeting_id}&recording_id=&lang=zh&type={doc_type}&uniq_meeting_id={uni_record_id}&tk=&id={record_id}&pwd=&activity_uid=&page_source=record&minutes_version=1&host=&from_share=1&enter_from=share&c_lang=zh-CN"
                
                print(f"📡 获取{doc_type}文档链接: {url}")

                conn.request("GET", url, headers=self.headers)
                res = conn.getresponse()
                data = res.read()

                print(f"📊 {doc_type}文档响应状态: {res.status}")
                print(f"📊 {doc_type}文档响应头: {dict(res.getheaders())}")

                # 处理gzip压缩
                try:
                    content_encoding = res.getheader('Content-Encoding', '')
                    if content_encoding == 'gzip':
                        print(f"🗜️ {doc_type}文档响应是gzip压缩，正在解压...")
                        decoded_data = gzip.decompress(data).decode("utf-8")
                    else:
                        decoded_data = data.decode("utf-8")

                    result = json.loads(decoded_data)
                    print(f"📄 {doc_type}文档响应: {decoded_data[:200]}...")
                except Exception as decode_error:
                    print(f"❌ {doc_type}文档解码失败: {str(decode_error)}")
                    conn.close()
                    continue

                conn.close()
                
                if result.get("code") == 0 and result.get("urls"):
                    document_urls[doc_type] = result.get("urls")
                    print(f"获取{doc_type}文档链接成功")
                else:
                    print(f"获取{doc_type}文档链接失败: {result.get('msg', '无可用链接')}")
                    
            except Exception as e:
                print(f"获取{doc_type}文档链接异常: {str(e)}")
                
        return document_urls
    
    def get_video_download_urls(self, uni_record_id):
        """
        获取视频和音频下载链接
        :param uni_record_id: 唯一记录ID
        :return: 视频音频下载链接列表
        """
        try:
            conn = http.client.HTTPSConnection("meeting.tencent.com")
            
            timestamp = int(time.time() * 1000)
            url = f"/wemeet-cloudrecording-webapi/v1/download/meeting?c_app_id=&c_os_model=web&c_os=web&c_os_version=Mozilla%2F5.0+%28Windows+NT+10.0%3B+Win64%3B+x64%29+AppleWebKit%2F537.36+%28KHTML%2C+like+Gecko%29+Chrome%2F130.0.0.0+Safari%2F537.36&c_timestamp={timestamp}&c_nonce=yDCPkzmsw&c_app_version=&c_instance_id=5&rnds=yDCPkzmsw&c_district=0&platform=Web&c_app_uid=&c_account_corp_id=*********&trace-id=2ea5ebee825d8edebad87b0080cdabea&id={uni_record_id}&pwd=&source=owner&activity_uid=&tk=&need_multi_stream=1&from_share=1&enter_from=share&c_lang=zh-CN"

            print(f"📡 获取视频下载链接: {url}")

            conn.request("GET", url, headers=self.headers)
            res = conn.getresponse()
            data = res.read()

            print(f"📊 视频响应状态: {res.status}")
            print(f"📊 视频响应头: {dict(res.getheaders())}")

            # 处理gzip压缩
            try:
                content_encoding = res.getheader('Content-Encoding', '')
                if content_encoding == 'gzip':
                    print("🗜️ 视频响应是gzip压缩，正在解压...")
                    decoded_data = gzip.decompress(data).decode("utf-8")
                else:
                    decoded_data = data.decode("utf-8")

                result = json.loads(decoded_data)
                print(f"📄 视频响应: {decoded_data[:200]}...")
            except Exception as decode_error:
                print(f"❌ 视频响应解码失败: {str(decode_error)}")
                conn.close()
                return []

            conn.close()

            if result.get("code") == 0:
                links = result.get("links", [])
                print(f"✅ 获取到 {len(links)} 个视频/音频链接")
                return links
            else:
                print(f"❌ 获取视频链接失败: {result}")
                return []
                
        except Exception as e:
            print(f"获取视频链接异常: {str(e)}")
            return []
    
    def create_meeting_directory(self, start_time, meeting_title):
        """
        根据会议开始时间创建目录
        :param start_time: 开始时间戳（毫秒）
        :param meeting_title: 会议标题
        :return: 目录路径
        """
        # 转换时间戳为日期时间
        dt = datetime.fromtimestamp(int(start_time) / 1000)
        date_str = dt.strftime("%Y%m%d_%H%M%S")
        
        # 清理会议标题中的非法字符
        safe_title = re.sub(r'[<>:"/\\|?*]', '_', meeting_title)
        
        # 创建目录名
        dir_name = f"{date_str}_{safe_title}"
        
        # 创建目录
        if not os.path.exists(dir_name):
            os.makedirs(dir_name)
            print(f"创建目录: {dir_name}")
        
        return dir_name
    
    def download_file(self, url, filepath, filename=None):
        """
        下载文件
        :param url: 下载链接
        :param filepath: 保存路径
        :param filename: 文件名（可选）
        :return: 是否成功
        """
        try:
            if filename:
                full_path = os.path.join(filepath, filename)
            else:
                # 从URL中提取文件名
                parsed_url = urllib.parse.urlparse(url)
                filename = os.path.basename(parsed_url.path)
                if not filename:
                    filename = "download_file"
                full_path = os.path.join(filepath, filename)
            
            print(f"开始下载: {filename}")
            
            # 创建请求对象
            req = urllib.request.Request(url)
            req.add_header('User-Agent', self.headers['User-Agent'])
            
            # 下载文件
            with urllib.request.urlopen(req) as response:
                with open(full_path, 'wb') as f:
                    f.write(response.read())
            
            print(f"下载完成: {full_path}")
            return True
            
        except Exception as e:
            print(f"下载文件失败 {url}: {str(e)}")
            return False

    def download_meeting_files(self, target_start_time=None, download_all=False):
        """
        下载会议文件
        :param target_start_time: 目标会议开始时间（毫秒时间戳字符串），如"1748570479000"
        :param download_all: 是否下载所有会议文件
        """
        print("开始获取会议列表...")
        meetings = self.get_meeting_list()

        if not meetings:
            print("未获取到会议列表")
            return

        print(f"获取到 {len(meetings)} 个会议记录")

        # 筛选要下载的会议
        target_meetings = []
        if download_all:
            target_meetings = meetings
        elif target_start_time:
            for meeting in meetings:
                if meeting.get("start_time") == target_start_time:
                    target_meetings.append(meeting)
                    break

        if not target_meetings:
            if target_start_time:
                print(f"未找到开始时间为 {target_start_time} 的会议")
            else:
                print("未指定要下载的会议")
            return

        # 下载每个会议的文件
        for meeting in target_meetings:
            self.download_single_meeting(meeting)

    def download_single_meeting(self, meeting):
        """
        下载单个会议的所有文件
        :param meeting: 会议信息字典
        """
        start_time = meeting.get("start_time")
        title = meeting.get("title", "未知会议")
        record_id = meeting.get("record_id")
        uni_record_id = meeting.get("uni_record_id")
        meeting_id = meeting.get("meeting_info", {}).get("meeting_id")
        record_type = meeting.get("record_type")

        print(f"\n开始处理会议: {title}")
        print(f"开始时间: {start_time}")
        print(f"记录类型: {record_type}")

        # 创建会议目录
        meeting_dir = self.create_meeting_directory(start_time, title)

        # 下载视频和音频文件
        if record_type in ["cloud_record", "fast_record"]:
            print("获取视频下载链接...")
            video_links = self.get_video_download_urls(uni_record_id)

            for i, link_info in enumerate(video_links):
                # 下载视频
                video_url = link_info.get("link")
                video_filename = link_info.get("filename")
                if video_url and video_filename:
                    self.download_file(video_url, meeting_dir, video_filename)

                # 下载音频
                audio_url = link_info.get("audio_link")
                audio_filename = link_info.get("audio_filename")
                if audio_url and audio_filename:
                    # 添加文件扩展名
                    if not audio_filename.endswith('.m4a'):
                        audio_filename += '.m4a'
                    self.download_file(audio_url, meeting_dir, audio_filename)

        # 下载文档文件（转写记录等）
        if record_type in ["realtime_transcription", "cloud_record"]:
            print("获取文档下载链接...")
            doc_urls = self.get_document_download_urls(
                meeting_id, record_id, uni_record_id,
                ['word', 'pdf', 'txt']
            )

            for doc_type, urls in doc_urls.items():
                for i, url in enumerate(urls):
                    # 生成文件名
                    filename = f"{title}_{doc_type}"
                    if len(urls) > 1:
                        filename += f"_{i+1}"

                    # 添加文件扩展名
                    if doc_type == 'word':
                        filename += '.docx'
                    elif doc_type == 'pdf':
                        filename += '.pdf'
                    elif doc_type == 'txt':
                        filename += '.txt'

                    self.download_file(url, meeting_dir, filename)

        print(f"会议 {title} 文件下载完成")


def main():
    """
    主函数 - 使用示例
    """
    # 请替换为您的Cookie
    cookie = """_qimei_uuid42=18c100b2d08100a8a8fbf14c894cfed7d943272246; _qimei_fingerprint=9a1333709ea85a9b315d1eb15950c460; _qimei_q36=; _ga_RPMZTEBERQ=GS1.1.1735105631.2.1.1735105719.0.0.0; _qimei_i_3=60c15380920c53dcc593ad665a8425e6f1e9a7f5100d0b87b58a7e582490756564363e943989e2a0b38e; hy_source=web; _gcl_au=1.1.1022510828.1748236605; hy_user=6a66ee4e7c9f42c7bc51292a37abe266; hy_token=vpwRvqa5aJvKcNXYU2K0ThrCqgjRjqFhpgy1aUc+Yv0s9h/sR38bKUm8RXzCMqCueFj/CEC+qCHMEC/JSJFAJA==; _qimei_h38=7de5d7f5a8fbf14c894cfed70200000f31930a; _qimei_i_1=2de24d85c10f558fc6c1fb6153d221b6f6eaf7f91b5f50d4e1db2f582493206c616361963980e1dc87f1e4c0; qcloud_from=qcloud.baidu.seo-1752558156326; qcstats_seo_keywords=%E9%80%9A%E7%94%A8%E6%8A%80%E6%9C%AF-%E7%BC%96%E7%A8%8B%E8%AF%AD%E8%A8%80-python%2C%E4%BA%91%E4%BA%A7%E5%93%81-%E5%AE%B9%E5%99%A8%E4%B8%8E%E4%B8%AD%E9%97%B4%E4%BB%B6-%E6%97%A5%E5%BF%97; sensorsdata2015jssdkcross=%7B%22distinct_id%22%3A%22100009325222%22%2C%22first_id%22%3A%22194f4284b9afe2-05e3105d450a0b-26011951-4953600-194f4284b9b2700%22%2C%22props%22%3A%7B%22%24latest_traffic_source_type%22%3A%22%E8%87%AA%E7%84%B6%E6%90%9C%E7%B4%A2%E6%B5%81%E9%87%8F%22%2C%22%24latest_utm_medium%22%3A%22cpc%22%2C%22%24search_keyword_id%22%3A%22f12eedaf0177fc23000000066881d78f%22%2C%22%24search_keyword_id_type%22%3A%22baidu_seo_keyword_id%22%2C%22%24search_keyword_id_hash%22%3A6719589322102133%7D%2C%22identities%22%3A%22eyIkaWRlbnRpdHlfY29va2llX2lkIjoiMTk0ZjQyODRiOWFmZTItMDVlMzEwNWQ0NTBhMGItMjYwMTE5NTEtNDk1MzYwMC0xOTRmNDI4NGI5YjI3MDAiLCIkaWRlbnRpdHlfbG9naW5faWQiOiIxMDAwMDkzMjUyMjIifQ%3D%3D%22%2C%22history_login_id%22%3A%7B%22name%22%3A%22%24identity_login_id%22%2C%22value%22%3A%22100009325222%22%7D%2C%22%24device_id%22%3A%22194f4284b9afe2-05e3105d450a0b-26011951-4953600-194f4284b9b2700%22%7D; we_meet_token=eJxUkV9vmzAUxb*LX7tMBtv8ibSHtWoyNRlpm5DSJ2TsC3ghQGxD0k777lMoUVPe*J1zbZ97-qLNcv1d1cbyWkCqJJoihr4NlLdt2g3EodRxmEuYg30ceoSQ0WLkLj3bBpeLPz5nFOHUKg0pzy1oNEUepgHGo9aDNqqphymX4sCl58lRzDqjajAGTdERshFatYfzU3xGfB-jIBx51RSqTu1bC4NdlNyOioReXTKNSEPxcWvgXRKoAk3R0w33CY7L4HVv2IKefonTjJyCZnXzclw1L63qG-JUwUPExbyNN9s5XxzrO-4uoHz0wnIWkdf1c-W*qkm0s-ywvd*6*8rwRZu85QUnh75hsWSwXJf8zwISmsT1LNIPXVLeHfvdRs2y6Pk2SnyNs8SqW7YMq9Ar-MfwEN93y7z-PZe9dW2e-Nz9uFr9dbSD2oMi3idodSM7Yb*YzPBDHSKoCPGE5iSf0ACTCQcXJhlImbsez5j8XI8k*uoALkTT1TYVjR5b9yjzmOPTSx*dAX2pw0H--gcAAP--PTK3Dw__; account_corp_id=*********; corp_id=*********; app_uid=144115235107096333; token_expire_time=**********; user_type=1; ACTIVITY_TICKET=eJxMkl1zojwcxb9Lbn3mMSEvgDN7sVZ3XaxF61axN0wgESPy0hCwdGe-%2A04ROubuf-45c05%2Akz-g9%2AP2fx7HRZ2b0LSlBBMA-utUJWRu1FFJDSbAgreDQkQIQtTCFEEbugxj3N-nZalEyE2ItQATAHu5EmnYrcAEIIhs23W-LPK9VFqG-Gi6EOTAwdVIXaki75ItAh2LfKb3S6Oyz57Ipti2IXTcXr8UicqHV1xlfOJmKKESMAFp8xL8XMzWeqcsX8Sb18P0PWuWlzjw9stR6nuL-dQr6m2iL3wusrympTT6IXBKfxNtfvl4ObtQz3uy2khoOD17LVnt34qqDvIzfzDZEz9F19ePlxVtZ-tD5o2r0TPbW%2AlptV40y8MuiPicUJaMk-FouYsif75dXdfPfrt1S8f7vuDpY7ypfpzPcvWtL65lcsPQz0I2KpZhR-MO8P1silTm4Zcx%2Aej1N5VJhdmdsXNZjB2xIzkTWBJGkYOYdI9YMiEoiejAfPgjcaHLWxwjlFFkk4F%2AXUk9wEfg778AAAD--w%2AAtUg_; lz_sign=KPcEAexvXPDfrJdvCsbqrj9rNhkVdwsj5lLzFVuyPzHm585djl2z7EF7FVkZIVUPT_gnmwuq5SInu8GvrMW3kWdBvPVaa17VxiYYAJ4-Yf0; lz_appid=*********; lz_uid=144115235107096333; lz_time=*************; landing_url=https://meeting.tencent.com/ct/24g6w13866; landing_path=https://meeting.tencent.com/ct/24g6w13866; landing_referralurl=; landing_referraldomain=; web_uid=97ce1f84-3627-47a8-b446-cc451a773ba2; _gid=GA1.2.274803522.1753770096; _gat_UA-205111495-1=1; lz_expire=1753788098946; _ga=GA1.2.1561455755.1739263069; _ga_6WSZ0YS5ZQ=GS2.1.s1753770095$o1$g1$t1753770139$j16$l0$h0"""

    # 创建下载器实例
    downloader = TencentMeetingDownloader(cookie)

    # 示例1: 下载指定开始时间的会议文件
    target_start_time = "1748570479000"  # 您提供的会议开始时间
    downloader.download_meeting_files(target_start_time=target_start_time)

    # 示例2: 下载所有会议文件（取消注释以使用）
    # downloader.download_meeting_files(download_all=True)


if __name__ == "__main__":
    main()
